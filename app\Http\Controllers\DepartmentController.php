<?php

namespace App\Http\Controllers;

use App\Models\Department;
use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Inertia\Inertia;

class DepartmentController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_departments')) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied'
                ], 403);
            }
            return redirect()->route('dashboard')->with('error', 'Access denied');
        }

        $query = Department::with(['users', 'roles'])
            ->withCount(['users', 'roles'])
            ->orderBy('name');

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->has('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->expectsJson()) {
            $departments = $query->paginate($request->get('per_page', 15));
            return response()->json([
                'success' => true,
                'data' => [
                    'departments' => $departments->items(),
                    'pagination' => [
                        'current_page' => $departments->currentPage(),
                        'last_page' => $departments->lastPage(),
                        'per_page' => $departments->perPage(),
                        'total' => $departments->total()
                    ]
                ]
            ]);
        }

        $departments = $query->get();

        return Inertia::render('Departments/Index', [
            'departments' => $departments,
            'filters' => $request->only(['search', 'status'])
        ]);
    }

    public function create(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_departments')) {
            return redirect()->route('departments.index')->with('error', 'Access denied');
        }

        $prefill = $request->only(['name', 'icon', 'color']);

        return Inertia::render('Departments/Create', [
            'prefill' => $prefill
        ]);
    }

    public function store(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_departments')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:departments,name',
            'description' => 'nullable|string',
            'color_code' => 'nullable|string|max:20',
            'icon' => 'nullable|string|max:50',
            'is_active' => 'boolean',
            'settings' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();
        $data['slug'] = Str::slug($data['name']);

        $department = Department::create($data);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Department created successfully',
                'data' => ['department' => $department]
            ], 201);
        }

        return redirect()->route('departments.index')->with('success', 'Department created successfully');
    }

    public function show(Request $request, Department $department)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('view_departments')) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied'
                ], 403);
            }
            return redirect()->route('departments.index')->with('error', 'Access denied');
        }

        $department->load(['users.role', 'roles', 'tasks' => function ($query) {
            $query->latest()->limit(10);
        }]);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => ['department' => $department]
            ]);
        }

        return Inertia::render('Departments/Show', [
            'department' => $department
        ]);
    }

    public function edit(Request $request, Department $department)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_departments')) {
            return redirect()->route('departments.index')->with('error', 'Access denied');
        }

        return Inertia::render('Departments/Edit', [
            'department' => $department
        ]);
    }

    public function update(Request $request, Department $department)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_departments')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255|unique:departments,name,' . $department->id,
            'description' => 'nullable|string',
            'color_code' => 'nullable|string|max:20',
            'icon' => 'nullable|string|max:50',
            'is_active' => 'sometimes|boolean',
            'settings' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();
        if (isset($data['name'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        $department->update($data);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Department updated successfully',
                'data' => ['department' => $department->fresh()]
            ]);
        }

        return redirect()->route('departments.index')->with('success', 'Department updated successfully');
    }

    public function destroy(Request $request, Department $department)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_departments')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        if ($department->users()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete department with assigned users'
            ], 422);
        }

        if ($department->roles()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete department with existing roles'
            ], 422);
        }

        $department->delete();

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Department deleted successfully'
            ]);
        }

        return redirect()->route('departments.index')->with('success', 'Department deleted successfully');
    }

    public function getStats(Request $request, Department $department)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('view_departments')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $stats = [
            'total_users' => $department->users()->count(),
            'active_users' => $department->users()->where('status', 'active')->count(),
            'available_users' => $department->users()->where('is_available', true)->count(),
            'total_roles' => $department->roles()->count(),
            'active_roles' => $department->roles()->where('is_active', true)->count(),
            'total_tasks' => $department->tasks()->count(),
            'pending_tasks' => $department->tasks()->where('status', 'pending')->count(),
            'completed_tasks' => $department->tasks()->where('status', 'completed')->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => ['stats' => $stats]
        ]);
    }

    public function getUsers(Request $request, Department $department)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('view_users')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $users = $department->users()
            ->with('role')
            ->orderBy('first_name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => ['users' => $users]
        ]);
    }

    public function getRoles(Request $request, Department $department)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('view_roles')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $roles = $department->roles()
            ->withCount('users')
            ->orderBy('level', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => ['roles' => $roles]
        ]);
    }
}
