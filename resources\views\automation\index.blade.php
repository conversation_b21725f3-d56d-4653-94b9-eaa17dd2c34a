@extends('layouts.app')

@section('title', 'Workflow Automation')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Workflow Automation</h1>
                    <p class="mt-1 text-sm text-gray-600">Automate your studio workflows and processes</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('automation.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Create Workflow
                    </a>
                </div>
            </div>
        </div>
    </div>

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Filters -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6">
                <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                        <input type="text" name="search" id="search" value="{{ request('search') }}" 
                               placeholder="Search workflows..." 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="trigger_type" class="block text-sm font-medium text-gray-700">Trigger Type</label>
                        <select name="trigger_type" id="trigger_type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">All Triggers</option>
                            @foreach($triggerTypes as $key => $label)
                                <option value="{{ $key }}" {{ request('trigger_type') === $key ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                        <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Filter
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Workflows Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @forelse($workflows as $workflow)
                <div class="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-cogs text-blue-600"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-lg font-medium text-gray-900">{{ $workflow->name }}</h3>
                                    <p class="text-sm text-gray-500">{{ $triggerTypes[$workflow->trigger_type] ?? $workflow->trigger_type }}</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $workflow->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $workflow->is_active ? 'Active' : 'Inactive' }}
                                </span>
                                <div class="relative">
                                    <button onclick="toggleDropdown('{{ $workflow->id }}')" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <div id="dropdown-{{ $workflow->id }}" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                                        <div class="py-1">
                                            <a href="{{ route('automation.show', $workflow) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">View</a>
                                            <a href="{{ route('automation.edit', $workflow) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Edit</a>
                                            <button onclick="toggleWorkflow('{{ $workflow->id }}')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                {{ $workflow->is_active ? 'Deactivate' : 'Activate' }}
                                            </button>
                                            <button onclick="deleteWorkflow('{{ $workflow->id }}')" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">Delete</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <p class="text-gray-600 text-sm mb-4">{{ $workflow->description ?: 'No description provided' }}</p>

                        <div class="mb-4">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-500">Actions:</span>
                                <span class="font-medium text-gray-900">{{ count($workflow->actions ?? []) }}</span>
                            </div>
                            <div class="flex items-center justify-between text-sm mt-1">
                                <span class="text-gray-500">Created by:</span>
                                <span class="font-medium text-gray-900">{{ $workflow->creator->first_name ?? 'Unknown' }}</span>
                            </div>
                            <div class="flex items-center justify-between text-sm mt-1">
                                <span class="text-gray-500">Created:</span>
                                <span class="font-medium text-gray-900">{{ $workflow->created_at->format('M j, Y') }}</span>
                            </div>
                        </div>

                        <div class="flex space-x-2">
                            <a href="{{ route('automation.show', $workflow) }}" class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-md text-sm font-medium text-center">
                                View
                            </a>
                            <a href="{{ route('automation.edit', $workflow) }}" class="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-2 rounded-md text-sm font-medium text-center">
                                Edit
                            </a>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-span-full">
                    <div class="text-center py-12">
                        <i class="fas fa-cogs text-gray-400 text-4xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No workflows found</h3>
                        <p class="text-gray-500 mb-4">Get started by creating your first automation workflow.</p>
                        <a href="{{ route('automation.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Create Workflow
                        </a>
                    </div>
                </div>
            @endforelse
        </div>
    </main>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Delete Workflow</h3>
        <p class="text-sm text-gray-600 mb-6">
            Are you sure you want to delete this workflow? This action cannot be undone.
        </p>
        <div class="flex justify-end space-x-3">
            <button onclick="closeDeleteModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium">
                Cancel
            </button>
            <button id="confirmDelete" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                Delete
            </button>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    let currentWorkflowId = null;

    // Auto-submit form on filter change
    document.querySelectorAll('select[name="trigger_type"], select[name="status"]').forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });

    function toggleDropdown(workflowId) {
        // Close all other dropdowns
        document.querySelectorAll('[id^="dropdown-"]').forEach(dropdown => {
            if (dropdown.id !== `dropdown-${workflowId}`) {
                dropdown.classList.add('hidden');
            }
        });
        
        // Toggle current dropdown
        const dropdown = document.getElementById(`dropdown-${workflowId}`);
        dropdown.classList.toggle('hidden');
    }

    function toggleWorkflow(workflowId) {
        fetch(`/automation/${workflowId}/toggle`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while toggling the workflow');
        });
    }

    function deleteWorkflow(workflowId) {
        currentWorkflowId = workflowId;
        document.getElementById('deleteModal').classList.remove('hidden');
        document.getElementById('deleteModal').classList.add('flex');
    }

    function closeDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
        document.getElementById('deleteModal').classList.remove('flex');
        currentWorkflowId = null;
    }

    document.getElementById('confirmDelete').addEventListener('click', function() {
        if (currentWorkflowId) {
            fetch(`/automation/${currentWorkflowId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting the workflow');
            });
        }
        closeDeleteModal();
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('[onclick^="toggleDropdown"]') && !event.target.closest('[id^="dropdown-"]')) {
            document.querySelectorAll('[id^="dropdown-"]').forEach(dropdown => {
                dropdown.classList.add('hidden');
            });
        }
    });
</script>
@endpush
