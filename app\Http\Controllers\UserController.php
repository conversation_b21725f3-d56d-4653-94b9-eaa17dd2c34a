<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Role;
use App\Models\Department;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Inertia\Inertia;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('view_users')) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied'
                ], 403);
            }
            return redirect()->route('dashboard')->with('error', 'Access denied');
        }

        $query = User::with(['department', 'role'])
            ->orderBy('created_at', 'desc');

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->has('department')) {
            $query->where('department_id', $request->department);
        }

        if ($request->has('role')) {
            $query->where('role_id', $request->role);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->expectsJson()) {
            $users = $query->paginate($request->get('per_page', 15));
            return response()->json([
                'success' => true,
                'data' => [
                    'users' => $users->items(),
                    'pagination' => [
                        'current_page' => $users->currentPage(),
                        'last_page' => $users->lastPage(),
                        'per_page' => $users->perPage(),
                        'total' => $users->total()
                    ]
                ]
            ]);
        }

        $users = $query->paginate(20);
        $departments = Department::active()->get();
        $roles = Role::active()->get();

        return Inertia::render('Users/Index', [
            'users' => $users,
            'departments' => $departments,
            'roles' => $roles,
            'filters' => $request->only(['search', 'department', 'role', 'status'])
        ]);
    }

    public function create(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('create_users')) {
            return redirect()->route('users.index')->with('error', 'Access denied');
        }

        $departments = Department::active()->get();
        $roles = Role::active()->get();

        return Inertia::render('Users/Create', [
            'departments' => $departments,
            'roles' => $roles
        ]);
    }

    public function store(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('create_users')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'nullable|string|max:20',
            'password' => 'required|string|min:8|confirmed',
            'department_id' => 'required|exists:departments,id',
            'role_id' => 'required|exists:roles,id',
            'hourly_rate' => 'nullable|numeric|min:0',
            'skills' => 'nullable|array',
            'skills.*' => 'string',
            'status' => 'required|in:active,inactive,pending',
            'is_available' => 'boolean'
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();
        $data['password'] = Hash::make($data['password']);

        $newUser = User::create($data);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'User created successfully',
                'data' => ['user' => $newUser->load(['department', 'role'])]
            ], 201);
        }

        return redirect()->route('users.index')->with('success', 'User created successfully');
    }

    public function show(Request $request, User $user)
    {
        $currentUser = $request->user();

        if (!$currentUser->hasPermission('*') && !$currentUser->hasPermission('view_users') && $currentUser->id !== $user->id) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied'
                ], 403);
            }
            return redirect()->route('users.index')->with('error', 'Access denied');
        }

        $user->load(['department', 'role']);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => ['user' => $user]
            ]);
        }

        return Inertia::render('Users/Show', [
            'user' => $user
        ]);
    }

    public function edit(Request $request, User $user)
    {
        $currentUser = $request->user();

        if (!$currentUser->hasPermission('*') && !$currentUser->hasPermission('edit_users') && $currentUser->id !== $user->id) {
            return redirect()->route('users.index')->with('error', 'Access denied');
        }

        $departments = Department::active()->get();
        $roles = Role::active()->get();

        return Inertia::render('Users/Edit', [
            'user' => $user,
            'departments' => $departments,
            'roles' => $roles
        ]);
    }

    public function update(Request $request, User $user)
    {
        $currentUser = $request->user();

        if (!$currentUser->hasPermission('*') && !$currentUser->hasPermission('edit_users') && $currentUser->id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'first_name' => 'sometimes|string|max:255',
            'last_name' => 'sometimes|string|max:255',
            'email' => 'sometimes|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'password' => 'nullable|string|min:8|confirmed',
            'department_id' => 'sometimes|exists:departments,id',
            'role_id' => 'sometimes|exists:roles,id',
            'hourly_rate' => 'nullable|numeric|min:0',
            'skills' => 'nullable|array',
            'skills.*' => 'string',
            'status' => 'sometimes|in:active,inactive,pending',
            'is_available' => 'sometimes|boolean'
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();
        
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        $user->update($data);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'User updated successfully',
                'data' => ['user' => $user->fresh(['department', 'role'])]
            ]);
        }

        return redirect()->route('users.index')->with('success', 'User updated successfully');
    }

    public function destroy(Request $request, User $user)
    {
        $currentUser = $request->user();

        if (!$currentUser->hasPermission('*') && !$currentUser->hasPermission('delete_users')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        if ($user->id === $currentUser->id) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete your own account'
            ], 422);
        }

        $user->delete();

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'User deleted successfully'
            ]);
        }

        return redirect()->route('users.index')->with('success', 'User deleted successfully');
    }

    public function toggleStatus(Request $request, User $user)
    {
        $currentUser = $request->user();

        if (!$currentUser->hasPermission('*') && !$currentUser->hasPermission('edit_users')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        if ($user->id === $currentUser->id) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot change your own status'
            ], 422);
        }

        $newStatus = $user->status === 'active' ? 'inactive' : 'active';
        $user->update(['status' => $newStatus]);

        return response()->json([
            'success' => true,
            'message' => "User status changed to {$newStatus}",
            'data' => ['user' => $user->fresh()]
        ]);
    }
}
