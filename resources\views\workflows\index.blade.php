@extends('layouts.app')

@section('title', 'Workflow Management')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Workflow Management</h1>
                    <p class="mt-1 text-sm text-gray-600">Manage project workflows and stages</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('workflows.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Create Workflow
                    </a>
                </div>
            </div>
        </div>
    </div>

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Filters -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6">
                <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                        <input type="text" name="search" id="search" value="{{ request('search') }}" 
                               placeholder="Search workflows..." 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="project_type" class="block text-sm font-medium text-gray-700">Project Type</label>
                        <select name="project_type" id="project_type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">All Types</option>
                            <option value="music_production" {{ request('project_type') === 'music_production' ? 'selected' : '' }}>Music Production</option>
                            <option value="video_production" {{ request('project_type') === 'video_production' ? 'selected' : '' }}>Video Production</option>
                            <option value="podcast" {{ request('project_type') === 'podcast' ? 'selected' : '' }}>Podcast</option>
                            <option value="mixing_mastering" {{ request('project_type') === 'mixing_mastering' ? 'selected' : '' }}>Mixing & Mastering</option>
                        </select>
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                        <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Filter
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Workflows Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @forelse($workflows as $workflow)
                <div class="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-project-diagram text-purple-600"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-lg font-medium text-gray-900">{{ $workflow->name }}</h3>
                                    <p class="text-sm text-gray-500">{{ ucfirst(str_replace('_', ' ', $workflow->project_type)) }}</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                @if($workflow->is_default)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Default
                                    </span>
                                @endif
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $workflow->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $workflow->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>

                        <p class="text-gray-600 text-sm mb-4">{{ $workflow->description ?: 'No description provided' }}</p>

                        <div class="mb-4">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-500">Stages:</span>
                                <span class="font-medium text-gray-900">{{ count($workflow->stages ?? []) }}</span>
                            </div>
                            <div class="flex items-center justify-between text-sm mt-1">
                                <span class="text-gray-500">Automation Rules:</span>
                                <span class="font-medium text-gray-900">{{ count($workflow->automation_rules ?? []) }}</span>
                            </div>
                            <div class="flex items-center justify-between text-sm mt-1">
                                <span class="text-gray-500">Created by:</span>
                                <span class="font-medium text-gray-900">{{ $workflow->creator->first_name ?? 'Unknown' }}</span>
                            </div>
                        </div>

                        <!-- Stage Preview -->
                        @if($workflow->stages && count($workflow->stages) > 0)
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-700 mb-2">Stages:</h4>
                                <div class="flex flex-wrap gap-1">
                                    @foreach(array_slice($workflow->stages, 0, 3) as $stage)
                                        <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                                            {{ $stage['name'] ?? 'Unnamed Stage' }}
                                        </span>
                                    @endforeach
                                    @if(count($workflow->stages) > 3)
                                        <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600">
                                            +{{ count($workflow->stages) - 3 }} more
                                        </span>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <div class="flex space-x-2">
                            <a href="{{ route('workflows.show', $workflow) }}" class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-md text-sm font-medium text-center">
                                View
                            </a>
                            <a href="{{ route('workflows.edit', $workflow) }}" class="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-2 rounded-md text-sm font-medium text-center">
                                Edit
                            </a>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-span-full">
                    <div class="text-center py-12">
                        <i class="fas fa-project-diagram text-gray-400 text-4xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No workflows found</h3>
                        <p class="text-gray-500 mb-4">Get started by creating your first workflow template.</p>
                        <a href="{{ route('workflows.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Create Workflow
                        </a>
                    </div>
                </div>
            @endforelse
        </div>

        <!-- Quick Templates -->
        @if($workflows->isEmpty())
            <div class="mt-12">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Quick Start Templates</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="createFromTemplate('music_production')">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
                            <i class="fas fa-music text-blue-600"></i>
                        </div>
                        <h4 class="font-medium text-gray-900">Music Production</h4>
                        <p class="text-sm text-gray-600 mt-1">Complete music production workflow</p>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="createFromTemplate('video_production')">
                        <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mb-3">
                            <i class="fas fa-video text-red-600"></i>
                        </div>
                        <h4 class="font-medium text-gray-900">Video Production</h4>
                        <p class="text-sm text-gray-600 mt-1">Video creation and editing workflow</p>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="createFromTemplate('podcast')">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mb-3">
                            <i class="fas fa-microphone text-green-600"></i>
                        </div>
                        <h4 class="font-medium text-gray-900">Podcast</h4>
                        <p class="text-sm text-gray-600 mt-1">Podcast recording and publishing</p>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="createFromTemplate('mixing_mastering')">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mb-3">
                            <i class="fas fa-sliders-h text-purple-600"></i>
                        </div>
                        <h4 class="font-medium text-gray-900">Mixing & Mastering</h4>
                        <p class="text-sm text-gray-600 mt-1">Audio mixing and mastering workflow</p>
                    </div>
                </div>
            </div>
        @endif
    </main>
</div>
@endsection

@push('scripts')
<script>
    // Auto-submit form on filter change
    document.querySelectorAll('select[name="project_type"], select[name="status"]').forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });

    function createFromTemplate(type) {
        window.location.href = `{{ route('workflows.create') }}?template=${type}`;
    }
</script>
@endpush
