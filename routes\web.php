<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\AnalyticsController;
use App\Http\Controllers\FinancialController;
use App\Http\Controllers\AccountingController;
use App\Http\Controllers\FileManagerController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\AutomationController;
use App\Http\Controllers\WorkflowController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\DepartmentController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [DashboardController::class, 'web'])->name('dashboard');
    Route::get('projects', function () {
        return Inertia::render('Projects/Index');
    })->name('projects.index');

    Route::get('projects/{project}', function ($project) {
        return Inertia::render('Projects/Show', ['project' => $project]);
    })->name('projects.show');

    Route::get('tasks', function () {
        return Inertia::render('Tasks/Index');
    })->name('tasks.index');

    Route::get('bookings', function () {
        return Inertia::render('Bookings/Index');
    })->name('bookings.index');

    Route::get('clients', function () {
        return Inertia::render('Clients/Index');
    })->name('clients.index');

    Route::get('chat', function () {
        return Inertia::render('Chat/Index');
    })->name('chat.index');

    Route::get('analytics', function () {
        return Inertia::render('Analytics/Index');
    })->name('analytics.index');

    Route::resource('workflows', WorkflowController::class);

    Route::resource('automation', AutomationController::class);
    Route::post('automation/{workflow}/toggle', [AutomationController::class, 'toggle'])->name('automation.toggle');
    Route::post('automation/{workflow}/execute', [AutomationController::class, 'execute'])->name('automation.execute');

    Route::resource('roles', RoleController::class);

    Route::resource('users', UserController::class);
    Route::post('users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');

    Route::resource('departments', DepartmentController::class);

    Route::get('financial', [FinancialController::class, 'dashboard'])->name('financial.dashboard');

    Route::get('accounting', [AccountingController::class, 'dashboard'])->name('accounting.dashboard');

    Route::get('file-manager', [FileManagerController::class, 'index'])->name('file-manager.index');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
